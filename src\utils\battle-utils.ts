"use client";

import { createClient } from "../../supabase/client";
import { toast } from "sonner";
import { getCurrentWord } from "./database";
import { currentWord, Difficulty, Player } from "@/interfaces/interfaces";

const supabase = createClient();

export const useSupabase = () => {
    return supabase
}

interface Word {
    text: string;
    difficulty: Difficulty;
}

// Mock data for demonstration
export const players: Player[] = [
    {
    id: "1",
    display_name: "Player One",
    avatar_url: "/path/to/avatar1.png",
    stats: {},
    lives: 3,
    score: 120,
    is_spectator: false,
    has_answered: false,
    lastAnswer: "correct"
    },
    {
    id: "2",
    display_name: "Player Two",
    avatar_url: "/path/to/avatar2.png",
    stats: {},
    lives: 2,
    score: 80,
    is_spectator: false,
    has_answered: false,
    lastAnswer: "incorrect"
    },
    {
    id: "3",
    display_name: "Spectator",
    avatar_url: "/path/to/avatar3.png",
    stats: {},
    lives: 0,
    score: 40,
    is_spectator: true,
    has_answered: false,
    lastAnswer: "pending"
    }
];

export const timeLeft = 15;
export const roundNumber = 1;
export const isAudioPlaying = false;

export const formatTime = (seconds: number) => {
return `${seconds < 10 ? "0" : ""}${seconds}`;
};

export const getActivePlayers = () => {
    return players.filter((player) => (player.lives || 0) > 0 && !player.is_spectator);
};

export const getEliminatedPlayers = () => {
    return players.filter((player) => (player.lives || 0) <= 0 || player.is_spectator);
};

export const fetchPlayersFromDatabase = async (roomName: string, matchId: string) => {
    try {
        const difficulty = roomName.toLowerCase();
        const { data: { user }, error: userError} = await supabase.auth.getUser();

    if (userError) {
        toast.error("Failed to fetch user data.");
        console.error("Error fetching user:", userError);
    }

    // fetch all players in this match with their player profile data
    const { data: matchPlayersData, error: matchPlayersError } = await supabase
        .from('match_players')
        .select(`
            player_id,
            score,
            lastAnswer,
            lives,
            has_answered,
            is_spectator,
            players (
            id,
            display_name,
            avatar_url
            )
        `)
        .eq('match_id', matchId)

    if (matchPlayersError) {
        toast.error("Failed to fetch match players.");
        console.error("Error fetching match players:", matchPlayersError);
        return [];
    }

    if (matchPlayersData && matchPlayersData.length > 0) {
        // convert database format to ourp layer interface
            const formattedPlayers: Player[] = matchPlayersData.map((matchPlayer: any) => {
                const playerProfile = matchPlayer.players;

                return {
                    id: matchPlayer.player_id,
                    display_name: playerProfile.display_name || 'Anonymous',
                    avatar_url: playerProfile?.avatar_url || undefined,
                    stats: playerProfile?.stats || {},
                    lives: matchPlayer.lives,
                    score: matchPlayer.score,
                    is_spectator: matchPlayer.is_spectator,
                    has_answered: matchPlayer.has_answered,
                    lastAnswer: matchPlayer.lastAnswer
                }
            })

        return formattedPlayers;
    }

    } catch (error) {
        console.error("Error fetching players:", error);
    }
}

export const getCurrentWordForMatch = async (matchId?: string): Promise<currentWord> => {
    if (matchId) {
        const wordData = await getCurrentWord(matchId);
        if (wordData) {
            return {
                text: wordData.word,
                difficulty: wordData.difficulty,
                audioUrl: wordData.audio_clip_url || undefined
            };
        }
    }

    // Fallback to mock data if no match ID or word data not found
    return {
        text: "",
        difficulty: "easy"
    };
}

export async function initiateSpellingTimer(
  matchId: string,
  setCountdownActive: (active: boolean) => void,
  setTimeLeft: (time: number) => void,
  initialTimeInSeconds: number
) {
  try {
    const { data: match, error: matchError} = await supabase
        .from('matches')
        .select('start_time, current_state')
        .eq('id', matchId)
        .single()

    if (matchError) {
        console.error("Error fetching waiting users:", matchError);
        return
    }
    const currentStartTime = match.start_time

    if (matchId && match.current_state === "spelling") {
      if (!currentStartTime) {
        const success = await attemptSetStartTime(matchId);
        if (success) {
          setCountdownActive(true);
        }
      } else if (currentStartTime) {
        setCountdownActive(true);
        // const success = await attemptResetStartTime(matchId);
        // if (success) {
        //   setCountdownActive(false);
        //   setTimeLeft(initialTimeInSeconds);
        // }
      }
      //  else if (currentStartTime) {
      //   setCountdownActive(true);
      // } else {
      //   setCountdownActive(false);
      // }
    }
  } catch (error) {
    console.error("Exception fetching waiting users:", error);
  }
}

async function attemptSetStartTime(matchId: string) {
  try {
    const { data: currentMatch, error: fetchError } = await supabase
      .from('matches')
      .select('start_time')
      .eq('id', matchId)
      .single();

    if (fetchError || !currentMatch) {
      console.error("Error fetching current match:", fetchError);
      return false;
    }

    const startTime = new Date();
    startTime.setSeconds(startTime.getSeconds() + 15);
    const startTimeISO = startTime.toISOString();

    const { data: updateResult, error: updateError } = await supabase
      .from('matches')
      .update({ start_time: startTimeISO })
      .eq('id', matchId)
      .is('start_time', null)
      .select();

    if (updateError) {
      console.error("Error setting start time:", updateError);
      return false;
    }

    return !!(updateResult && updateResult.length > 0);
  } catch (error) {
    console.error("Exception setting start time:", error);
    return false;
  }
}

async function attemptResetStartTime(matchId: string) {
  try {
    const { data: resetResult, error: resetError } = await supabase
      .from('matches')
      .update({ start_time: null })
      .eq('id', matchId)
      .select();

    if (resetError) {
      console.error("Error resetting start time:", resetError);
      return false;
    }

    return !!(resetResult && resetResult.length > 0);
  } catch (error) {
    console.error("Exception resetting start time:", error);
    return false;
  }
}

export async function performHeartbeatSyncBattle(
  supabase: ReturnType<typeof createClient>,
  currentMatchId: string | null,
  countdownActive: boolean,
  setCountdownActive: (active: boolean) => void,
  setTimeLeft: (time: number) => void,
  initialTimeInSeconds: number,
) {
  try {
    if (!currentMatchId) return;

    const { data: currentMatch, error } = await supabase
      .from('matches')
      .select('id, status, start_time')
      .eq('id', currentMatchId)
      .single();

    if (error || !currentMatch) {
      console.error("Heartbeat sync error:", error);
      return;
    }

    const now = new Date();
    const startTime = currentMatch.start_time ? new Date(currentMatch.start_time) : null;

    if (startTime) {
      if (!countdownActive) {
        setCountdownActive(true);
        const newTimeLeft = Math.max(0, Math.floor((startTime.getTime() - now.getTime()) / 1000));
        setTimeLeft(newTimeLeft);
      }
    } else {
      if (countdownActive) {
        setCountdownActive(false);
        setTimeLeft(initialTimeInSeconds);
      }
    }
    if (currentMatch.start_time == null) {
      return 15
    }
    return currentMatch.start_time
  } catch (error) {
    console.error("Exception in heartbeat sync:", error);
  }
}

export async function updatePendingPlayers(matchId: string) {
  try {
    const { data: matchPlayers, error } = await supabase
      .from('match_players')
      .select('player_id, lastAnswer, lives, has_answered')
      .eq('match_id', matchId);

    if (error) {
      console.error("Error fetching match players for update:", error);
      return;
    }

    if (matchPlayers && matchPlayers.length > 0) {
      const updates = matchPlayers
        .filter(player => player.lastAnswer === "pending")
        .map(player => ({
          match_id: matchId, // Add match_id here
          player_id: player.player_id,
          lastAnswer: "incorrect",
          lives: (player.lives || 0) - 1,
        }));

      if (updates.length > 0) {
        const { error: updateError } = await supabase
          .from('match_players')
          .upsert(updates, { onConflict: 'match_id,player_id' });

        if (updateError) {
          console.error("Error updating pending players:", updateError);
        }
      }
    }
  } catch (error) {
    console.error("Exception in updatePendingPlayers:", error);
  }
}

export async function updatePlayersHasAnswered(matchId: string) {
  const { error } = await supabase
    .from('match_players')
    .update({ has_answered: false })
    .eq('match_id', matchId)

  if (error) {
    console.error(error)
  }
}